"use client";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { Card } from "~/components/Card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "~/components/ui/form";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { useStore } from "~/hooks/store";
import { useShotputTags } from "~/hooks/useShotputTags";
import {
  ShotputHand,
  shotputHandOptions,
  ShotputMovement,
  shotputMovementOptions,
  ShotputType,
  shotputTypeOptions,
} from "~/lib/enums/shotput";
import type { ShotputThrows } from "~/server/db/schema";
import { api } from "~/trpc/react";

const radioGroups = [
  { key: "movement", options: shotputMovementOptions },
  { key: "type", options: shotputTypeOptions },
  { key: "hand", options: shotputHandOptions },
];

export const ShotputThrowForm = ({ disabled }: { disabled?: boolean }) => {
  const setShotputThrow = useStore((state) => state.setShotputThrow);

  const { tags } = useShotputTags();
  const throws = tags?.throws;

  const firstThrow = throws?.[0];

  const { mutate: updateComment } = api.shotput.updateComment.useMutation({
    onSuccess: () => {
      toast.success("Comment updated successfully");
    },
    onError: () => {
      toast.error("Failed to update comment");
    },
  });

  const form = useForm<ShotputThrows>({
    defaultValues: {
      number: firstThrow?.number ?? 1,
      movement: firstThrow?.movement ?? ShotputMovement.Rotation,
      type: firstThrow?.type ?? ShotputType.Full,
      hand: firstThrow?.hand ?? ShotputHand.RightHand,
      comment: firstThrow?.comment ?? "",
      id: firstThrow?.id,
    },
  });

  // Update form when firstThrow changes
  useEffect(() => {
    if (firstThrow) {
      form.reset({
        number: firstThrow.number,
        movement: firstThrow.movement,
        type: firstThrow.type,
        hand: firstThrow.hand,
        comment: firstThrow.comment ?? "",
        id: firstThrow.id,
      });
    }
  }, [firstThrow, form]);
  return (
    <Card>
      <Form {...form}>
        <form
          // onChange={form.handleSubmit(setShotputThrow)}
          className="space-y-2.5"
        >
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem className="grid gap-[5px]">
                <p className="text-smallLabel text-black/60">Throw Number:</p>
                <FormControl className="mt-0 w-full">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      const throwObj = throws?.find((x) => x.number === +value);
                      setShotputThrow(throwObj);
                      if (throwObj) {
                        form.setValue("movement", throwObj.movement);
                        form.setValue("type", throwObj.type);
                        form.setValue("hand", throwObj.hand);
                        form.setValue("comment", throwObj.comment ?? "");
                        form.setValue("id", throwObj.id);
                      }
                    }}
                    defaultValue={field.value.toString()}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select athlete" />
                    </SelectTrigger>
                    <SelectContent>
                      {throws?.map((x) => (
                        <SelectItem key={x.id} value={x.number.toString()}>
                          {x.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {radioGroups.map((group) => (
            <FormField
              key={group.key}
              control={form.control}
              name={group.key as keyof ShotputThrows}
              render={({ field }) => (
                <FormItem className="flex gap-[5px]">
                  <p className="w-[53px] text-smallLabel capitalize text-black/60">
                    {group.key}:
                  </p>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      disabled={disabled}
                      value={field.value as string}
                      className="grid flex-1 grid-cols-3 gap-[5px]"
                    >
                      {group.options.map((x) => (
                        <FormItem
                          key={x.value}
                          className="flex items-center gap-0.5"
                        >
                          <RadioGroupItem value={x.value} text={x.key} />
                          <p className="text-[10px] leading-[8px] tracking-[0.28px]">
                            {x.value}
                          </p>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          ))}

          {/* Comment Textarea */}
          <FormField
            control={form.control}
            name="comment"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-[5px]">
                <p className="text-smallLabel text-black/60">Comment:</p>
                <FormControl>
                  <Textarea
                    className="min-h-[60px] w-full resize-none"
                    value={field.value ?? ""}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                    }}
                    onBlur={() => {
                      const currentThrow = form.getValues();
                      const throwId = currentThrow.id;
                      const comment = field.value ?? "";

                      if (throwId && typeof throwId === "number") {
                        updateComment({
                          id: throwId,
                          comment: comment,
                        });
                      } else {
                        console.warn(
                          "Cannot update comment: missing or invalid throw ID",
                          { throwId, currentThrow },
                        );
                        toast.error(
                          "Cannot update comment: throw not properly selected",
                        );
                      }
                    }}
                    disabled={disabled}
                    placeholder="Add any notes about the throw..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </Card>
  );
};
